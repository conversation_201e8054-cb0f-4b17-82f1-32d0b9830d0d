import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule, FormBuilder } from '@angular/forms';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { MultiFieldComponent } from './multi-field.component';

describe('MultiFieldComponent', () => {
  let component: MultiFieldComponent;
  let fixture: ComponentFixture<MultiFieldComponent>;
  let formBuilder: FormBuilder;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        MultiFieldComponent,
        ReactiveFormsModule,
        HttpClientTestingModule,
        NoopAnimationsModule
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(MultiFieldComponent);
    component = fixture.componentInstance;
    formBuilder = TestBed.inject(FormBuilder);

    // Setup basic inputs
    component.field = {
      fieldName: 'testField',
      isMulti: true,
      type: 'string',
      mandatory: false
    };
    component.formGroup = formBuilder.group({});
    component.fields = [];

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have required inputs', () => {
    expect(component.field).toBeDefined();
    expect(component.formGroup).toBeDefined();
    expect(component.fields).toBeDefined();
  });

  it('should emit fieldValueChange events', () => {
    spyOn(component.fieldValueChange, 'emit');
    const testEvent = { field: 'test', value: 'value' };

    component.onFieldValueChange(testEvent);

    expect(component.fieldValueChange.emit).toHaveBeenCalledWith(testEvent);
  });

  describe('Multi-field operations', () => {
    beforeEach(() => {
      // Setup a FormArray for testing
      const multiArray = formBuilder.array([
        formBuilder.group({ testField: ['initial value'] })
      ]);
      component.formGroup.addControl('testField', multiArray);
    });

    it('should create multi-field FormGroup', () => {
      const field = {
        fieldName: 'testField',
        mandatory: true,
        noInput: false
      };

      const result = component.createMultiField(field);

      expect(result).toBeDefined();
      expect(result.get('testField')).toBeDefined();
      expect(result.get('testField')?.hasError('required')).toBeTruthy();
    });

    it('should get multi array for simple field', () => {
      const result = component.getMultiArray('testField');

      expect(result).toBeDefined();
      expect(result.length).toBe(1);
    });

    it('should add multi-field entry', () => {
      const field = {
        fieldName: 'testField',
        mandatory: false
      };

      const initialLength = component.getMultiArray('testField').length;
      component.addMultiField(field);
      const newLength = component.getMultiArray('testField').length;

      expect(newLength).toBe(initialLength + 1);
    });

    it('should remove multi-field entry', () => {
      // Add another entry first
      const field = { fieldName: 'testField', mandatory: false };
      component.addMultiField(field);

      const initialLength = component.getMultiArray('testField').length;
      component.removeMultiField('testField', 0);
      const newLength = component.getMultiArray('testField').length;

      expect(newLength).toBe(initialLength - 1);
    });
  });

  describe('Group path parsing', () => {
    it('should parse simple group path', () => {
      const result = component.parseGroupPath('simpleGroup');

      expect(result.parent).toBe('simpleGroup');
      expect(result.child).toBeNull();
      expect(result.isNested).toBeFalsy();
    });

    it('should parse nested group path', () => {
      const result = component.parseGroupPath('parent|child');

      expect(result.parent).toBe('parent');
      expect(result.child).toBe('child');
      expect(result.isNested).toBeTruthy();
    });

    it('should handle whitespace in group paths', () => {
      const result = component.parseGroupPath(' parent | child ');

      expect(result.parent).toBe('parent');
      expect(result.child).toBe('child');
      expect(result.isNested).toBeTruthy();
    });
  });
});
