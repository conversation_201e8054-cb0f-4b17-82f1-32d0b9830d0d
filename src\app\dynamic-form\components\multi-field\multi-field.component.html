<!-- Multi Field Component Template -->
<!-- Section 1: Non-grouped Multi-fields (Main Implementation) -->
@if (field.isMulti) {
  <div [formArrayName]="field.fieldName">
    @for (control of getMultiArray(field.fieldName).controls; track control; let j = $index) {
      <div [formGroupName]="j" class="form-field is-multi">
        @if (field.foreginKey) {
          <app-regular-field
            [field]="field"
            [form]="$any(control)"
            [isViewMode]="isViewMode"
            [fields]="fields"
            [multiIndex]="j + 1"
            (fieldValueChange)="onFieldValueChange($event)">
          </app-regular-field>
        } @else {
          <label>{{ field.fieldName }} ({{ j + 1 }})
            @if (field.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
          </label>

          <div class="multi-input-container">
            <div class="multi-input">
              <!-- Regular input fields for non-foreign key multi-fields -->
              @if (field.type === 'string') {
                <input [formControlName]="field.fieldName" type="text"
                  [placeholder]="(field.label?.trim() || field.fieldName) + '-' + (j+1)" [disabled]="field.noInput" />
              }
              @if (field.type === 'int') {
                <input [formControlName]="field.fieldName" type="number" [disabled]="field.noInput" />
              }
              @if (field.type === 'boolean') {
                <input [formControlName]="field.fieldName" type="checkbox" [disabled]="field.noInput" />
              }
              @if (field.type === 'date') {
                <input [formControlName]="field.fieldName" type="date" [disabled]="field.noInput" />
              }
              @if (field.type === 'double') {
                <input [formControlName]="field.fieldName" type="number"
                  step="00.50" [disabled]="field.noInput" />
              }
            </div>

            <div class="multi-buttons">
              @if (getMultiArray(field.fieldName).length > 1 && !isViewMode && !field.noInput) {
                <button mat-icon-button color="warn" type="button" (click)="removeMultiField(field.fieldName, j)" matTooltip="Delete">
                  <mat-icon>delete</mat-icon>
                </button>
              }

              @if (!isViewMode && !field.noInput) {
                <button mat-icon-button color="primary" type="button" (click)="addMultiField(field, undefined, j)" matTooltip="Add">
                  <mat-icon>add</mat-icon>
                </button>
              }
            </div>
          </div>
        }
      </div>
    }
  </div>
}

<!-- Section 2: Grouped Multi-fields with Row View Buttons -->
@if (field.isMulti && groupIndex !== undefined && groupName) {
  <div class="row-view-table-cell-multi" [formArrayName]="field.fieldName">
    <label>{{ field.fieldName }}
      @if (field.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
    </label>
    @for (multiControl of getMultiArray(field.fieldName, groupIndex, groupName).controls; track multiControl; let l = $index) {
      <div [formGroupName]="l" class="row-view-multi-item">
        <div class="row-view-multi-input">
          @if (field.type === 'string') {
            <input [formControlName]="field.fieldName" type="text"
              [placeholder]="(field.label?.trim() || field.fieldName) + ' (' + (l + 1) + ')'" [disabled]="field.noInput" />
          }
          @if (field.type === 'int') {
            <input [formControlName]="field.fieldName" type="number" [disabled]="field.noInput" />
          }
          @if (field.type === 'boolean') {
            <input [formControlName]="field.fieldName" type="checkbox" [disabled]="field.noInput" />
          }
          @if (field.type === 'date') {
            <input [formControlName]="field.fieldName" type="date" [disabled]="field.noInput" />
          }
          @if (field.type === 'double') {
            <input [formControlName]="field.fieldName" type="number" step="0.01" [disabled]="field.noInput" />
          }
          @if (field.foreginKey) {
            <app-regular-field
              [field]="field"
              [form]="$any(multiControl)"
              [isViewMode]="isViewMode"
              [fields]="fields"
              [multiIndex]="l + 1"
              (fieldValueChange)="onFieldValueChange($event)">
            </app-regular-field>
          }
        </div>
        <div class="row-view-multi-buttons">
          @if (getMultiArray(field.fieldName, groupIndex, groupName).controls.length > 1 && !isViewMode && !field.noInput) {
            <button mat-icon-button color="warn" type="button" (click)="removeMultiField(field.fieldName, l, groupIndex, groupName)" matTooltip="Delete">
              <mat-icon>delete</mat-icon>
            </button>
          }
          @if (!isViewMode && !field.noInput) {
            <button mat-icon-button color="primary" type="button" (click)="addMultiField(field, groupIndex, l, groupName)" matTooltip="Add">
              <mat-icon>add</mat-icon>
            </button>
          }
        </div>
      </div>
    }
  </div>
}

<!-- Section 3: Nested Group Multi-fields -->
@if (field.isMulti && groupIndex !== undefined && groupName && nestedGroupIndex !== undefined) {
  <div [formArrayName]="field.fieldName">
    <h5>{{ field.fieldName }}
      @if (field.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
    </h5>

    @for (multiControl of getMultiArray(field.fieldName, groupIndex, groupName, nestedGroupIndex).controls; track multiControl; let m = $index) {
      <div [formGroupName]="m" class="form-field is-multi">
        <div class="multi-input-container">
          <div class="multi-input">
            <app-regular-field
              [field]="field"
              [form]="$any(multiControl)"
              [isViewMode]="isViewMode"
              [fields]="fields"
              [multiIndex]="m + 1"
              (fieldValueChange)="onFieldValueChange($event)">
            </app-regular-field>
          </div>

          <div class="multi-buttons">
            @if (getMultiArray(field.fieldName, groupIndex, groupName, nestedGroupIndex).controls.length > 1 && !isViewMode && !field.noInput) {
              <button mat-icon-button color="warn" type="button" (click)="removeMultiField(field.fieldName, m, groupIndex, groupName, nestedGroupIndex)" matTooltip="Delete">
                <mat-icon>delete</mat-icon>
              </button>
            }

            @if (!isViewMode && !field.noInput) {
              <button mat-icon-button color="primary" type="button" (click)="addMultiField(field, groupIndex, m, groupName, nestedGroupIndex)" matTooltip="Add">
                <mat-icon>add</mat-icon>
              </button>
            }
          </div>
        </div>
      </div>
    }
  </div>
}
