/* Multi Field Component Styles */

/* ===== CORE MULTI-FIELD STYLING ===== */

/* Multi-field container layout */
.multi-field, .group-fields {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  flex-wrap: wrap;
  background-color: white;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  margin-bottom: 16px;
  position: relative;
}

/* Main multi-field input container */
.is-multi .multi-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

/* Multi-field input area */
.is-multi .multi-input {
  flex: 1;
  min-width: 0; /* Allow flex item to shrink */
}

/* Multi-field buttons container */
.is-multi .multi-buttons {
  display: flex;
  flex-direction: row;
  gap: 4px;
  flex-shrink: 0;
  align-items: center;
}

/* General multi-field styling */
.multi-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.multi-input {
  flex: 1;
  min-width: 0;
}

.multi-buttons {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
}

/* Material multi-field styling */
.multi-field {
  width: 100%;
  margin-bottom: 16px;
  box-sizing: border-box;
}

/* ===== ROW VIEW MULTI-FIELD STYLING ===== */

/* Multi-field in Table */
.row-view-table-cell-multi {
  flex: 1;
  min-width: 200px;
  display: flex;
  flex-direction: column;
  min-width: 180px;
  max-width: 250px;
}

/* Multi-field styling in row view */
.row-view-multi-item {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 3px;
}

.row-view-multi-input {
  flex: 1;
  min-width: 0;
}

.row-view-multi-input input,
.row-view-multi-input select {
  width: 100%;
  padding: 4px 6px;
  font-size: 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background-color: white;
}

.row-view-multi-input input:focus,
.row-view-multi-input select:focus {
  border-color: #9e9e9e;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(158, 158, 158, 0.25);
}

.row-view-multi-buttons {
  display: flex;
  gap: 2px;
  flex-shrink: 0;
}

.row-view-multi-buttons .mat-mdc-icon-button {
  width: 24px !important;
  height: 24px !important;
  padding: 0 !important;
}

.row-view-multi-buttons .mat-icon {
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
}

/* ===== NESTED GROUP MULTI-FIELD STYLING ===== */

/* Improved spacing for nested multi-fields */
.nested-group-section .form-field h5 {
  font-size: 13px;
  font-weight: 500;
  color: #495057;
  margin-bottom: 8px;
}

/* Multi-field styling in nested groups */
.nested-group-section .form-field.is-multi {
  margin-bottom: 16px;
}

.nested-group-section .multi-input-container {
  display: flex;
  align-items: center;
  gap: 6px;
  width: 100%;
}

.nested-group-section .multi-input {
  flex: 1;
  min-width: 0;
}

.nested-group-section .multi-buttons {
  display: flex;
  gap: 3px;
  flex-shrink: 0;
  align-items: center;
}

.nested-group-section .multi-input input,
.nested-group-section .multi-input select {
  width: 100%;
}

/* Visual distinction for nested multi-fields */
.nested-group-section .form-field.is-multi label {
  color: #495057;
  font-weight: 600;
  font-size: 13px;
}

.nested-group-section .form-field.is-multi .multi-input-container {
  background-color: white;
  border: 1px solid #f1f3f4;
  border-radius: 4px;
  padding: 8px;
}

/* Nested group multi-field label styling */
.nested-group-section .form-field.is-multi label {
  color: #000000;
  font-weight: 600;
  font-size: 13px;
}

/* ===== READ-ONLY STYLING ===== */

/* No Input Indicator Styling */
.no-input-indicator {
  color: #e74c3c;
  font-size: 11px;
  font-weight: normal;
  font-style: italic;
  margin-left: 4px;
}

/* Unified background for disabled/readonly fields */
input[disabled],
select[disabled],
textarea[disabled],
input[readonly],
select[readonly],
textarea[readonly] {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  cursor: not-allowed !important;
  border-color: #dadce0 !important;
  opacity: 1 !important;
}

/* Hover state for disabled/readonly fields */
input[disabled]:hover,
select[disabled]:hover,
textarea[disabled]:hover,
input[readonly]:hover,
select[readonly]:hover,
textarea[readonly]:hover {
  background-color: #f1f3f4 !important;
  border-color: #dadce0 !important;
}

/* Focus state for disabled/readonly fields (should not be focusable but just in case) */
input[disabled]:focus,
select[disabled]:focus,
textarea[disabled]:focus,
input[readonly]:focus,
select[readonly]:focus,
textarea[readonly]:focus {
  background-color: #f1f3f4 !important;
  border-color: #dadce0 !important;
  box-shadow: none !important;
  outline: none !important;
}

/* ===== RESPONSIVE DESIGN ===== */

/* Responsive group styling */
@media (max-width: 768px) {
  .multi-field, .group-fields {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    padding: 12px;
  }

  /* Responsive multi-field adjustments */
  .multi-input-container {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .multi-buttons {
    justify-content: flex-end;
  }

  .mat-mdc-chip-row {
    min-height: 28px;
    padding: 0 6px;
  }

  /* Row view responsive adjustments */
  .row-view-multi-input input,
  .row-view-multi-input select {
    padding: 3px 5px;
    font-size: 11px;
  }

  .row-view-multi-buttons .mat-mdc-icon-button {
    width: 20px !important;
    height: 20px !important;
  }

  .row-view-multi-buttons .mat-icon {
    font-size: 14px !important;
    width: 14px !important;
    height: 14px !important;
  }

  /* Responsive nested multi-field styling */
  .nested-group-section .multi-input-container {
    flex-direction: column;
    align-items: stretch;
    gap: 6px;
  }

  .nested-group-section .multi-buttons {
    justify-content: flex-end;
  }

  /* Responsive no-input indicator */
  .no-input-indicator {
    font-size: 10px;
    font-weight: normal;
    color: #e74c3c;
    margin-left: 2px;
  }
}

@media (max-width: 480px) {
  .multi-field, .group-fields {
    padding: 8px;
    gap: 8px;
  }

  .multi-input-container {
    gap: 6px;
  }

  .multi-buttons {
    gap: 2px;
  }

  /* Extra small screen no-input indicator */
  .no-input-indicator {
    font-size: 9px;
    font-weight: normal;
    color: #e74c3c;
    margin-left: 1px;
  }
}

/* ===== MATERIAL UI COMPONENT STYLING ===== */

/* Material chip list styling */
.mat-mdc-chip-listbox {
  width: 100%;
  margin-bottom: 16px;
}

/* Material chip input styling */
.mat-mdc-chip-input {
  flex: 1;
  min-width: 0;
}

/* Material chip row styling */
.mat-mdc-chip-row {
  min-height: 32px;
  padding: 0 8px;
}

/* Button disabled state */
.action-button:disabled {
  background-color: #e0e0e0;
  color: #b0b0b0;
  cursor: not-allowed;
}
