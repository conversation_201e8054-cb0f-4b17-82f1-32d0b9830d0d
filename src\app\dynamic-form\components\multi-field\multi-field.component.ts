import { Component, Input, Output, EventEmitter, <PERSON><PERSON><PERSON>roy, OnInit, inject, ChangeDetectorRef } from '@angular/core';
import { FormGroup, FormArray, FormBuilder, Validators } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { environment } from '../../../../environments/environment';
import { RegularFieldComponent } from '../regular-field/regular-field.component';

@Component({
  selector: 'app-multi-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    RegularFieldComponent
  ],
  templateUrl: './multi-field.component.html',
  styleUrl: './multi-field.component.scss'
})
export class MultiFieldComponent implements OnInit, OnDestroy {
  @Input() field: any;
  @Input() formGroup!: FormGroup;
  @Input() isViewMode: boolean = false;
  @Input() fields: any[] = [];
  @Input() groupIndex?: number;
  @Input() groupName?: string;
  @Input() nestedGroupIndex?: number;
  @Input() isRowView: boolean = false;

  @Output() fieldValueChange = new EventEmitter<any>();

  private fb = inject(FormBuilder);
  private http = inject(HttpClient);
  private cdr = inject(ChangeDetectorRef);

  constructor() { }

  ngOnInit() {
    // Multi-field component initialization
  }

  ngOnDestroy() {
    // Cleanup handled by parent component
  }

  createMultiField(field: any): FormGroup {
    const group = this.fb.group({});
    if (Array.isArray(field)) {
      field.forEach(fieldName => {
        const control = this.fb.control('', Validators.required);
        group.addControl(fieldName, control);
      });
    } else {
      const control = this.fb.control("", field.mandatory ? Validators.required : null);
      group.addControl(field.fieldName, control);

      // Disable control if noInput is true
      if (field.noInput) {
        control.disable({ emitEvent: false });
      }
    }
    return group;
  }

  getMultiArray(fieldName: string, groupIndex?: number, groupName?: string, nestedGroupIndex?: number): FormArray {
    if (groupIndex !== undefined && groupName) {
      // Check if this is a nested group
      const parsed = this.parseGroupPath(groupName);
      if (parsed.isNested && parsed.parent && parsed.child && nestedGroupIndex !== undefined) {
        // Navigate to nested group: parent[groupIndex].child[nestedGroupIndex].fieldName
        const parentArray = this.getGroupArray(parsed.parent);
        const parentGroup = parentArray.at(groupIndex) as FormGroup;
        const childArray = parentGroup.get(parsed.child) as FormArray;
        const childGroup = childArray.at(nestedGroupIndex) as FormGroup;
        return childGroup.get(fieldName) as FormArray;
      } else {
        // Regular group
        const groupArray = this.getGroupArray(groupName);
        const group = groupArray.at(groupIndex) as FormGroup;
        return group.get(fieldName) as FormArray;
      }
    } else {
      return this.formGroup.get(fieldName) as FormArray;
    }
  }

  addMultiField(field: any, groupIndex?: number, index?: number, groupName?: string, nestedGroupIndex?: number) {
    try {
      const multiArray = this.getMultiArray(field.fieldName, groupIndex, groupName, nestedGroupIndex);
      const newField = this.createMultiField(field);
      if (index !== undefined) {
        multiArray.insert(index + 1, newField);
      } else {
        multiArray.push(newField);
      }

    } catch (error) {
      // Handle error silently
    }
  }

  removeMultiField(fieldName: string, index: number, groupIndex?: number, groupName?: string, nestedGroupIndex?: number) {
    const multiArray = this.getMultiArray(fieldName, groupIndex, groupName, nestedGroupIndex);
    multiArray.removeAt(index);
  }

  onFieldValueChange(event: any) {
    this.fieldValueChange.emit(event);
  }

  // Helper methods for group navigation
  parseGroupPath(groupPath: string): { parent: string | null, child: string | null, isNested: boolean } {
    // Trim the entire groupPath first to handle trailing spaces
    const trimmedGroupPath = groupPath.trim();

    if (trimmedGroupPath.includes('|')) {
      const parts = trimmedGroupPath.split('|');
      return {
        parent: parts[0].trim(),
        child: parts[1].trim(),
        isNested: true
      };
    }
    return {
      parent: trimmedGroupPath.trim(),
      child: null,
      isNested: false
    };
  }

  getGroupArray(groupName: string): FormArray {
    return this.formGroup.get(groupName) as FormArray;
  }
}
